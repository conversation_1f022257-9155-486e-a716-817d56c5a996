<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="80%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="detectionOrder" label="检测次序">
            <el-input v-model="dataForm.detectionOrder" placeholder="检测次序"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="stationName" label="测点名称">
            <el-input v-model="dataForm.stationName" placeholder="测点名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="concentratedVolume" label="浓缩体积">
            <el-input v-model="dataForm.concentratedVolume" placeholder="浓缩体积"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="phylum" label="门">
            <el-input v-model="dataForm.phylum" placeholder="门"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="className" label="纲">
            <el-input v-model="dataForm.className" placeholder="纲"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="orderName" label="目">
            <el-input v-model="dataForm.orderName" placeholder="目"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="familyName" label="科">
            <el-input v-model="dataForm.familyName" placeholder="科"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="genusName" label="属">
            <el-input v-model="dataForm.genusName" placeholder="属"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="species" label="种类">
            <el-input v-model="dataForm.species" placeholder="种类"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="latinName" label="拉丁文">
            <el-input v-model="dataForm.latinName" placeholder="拉丁文"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="wetWeight" label="湿重">
            <el-input v-model="dataForm.wetWeight" placeholder="湿重"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="countNumber" label="个数">
            <el-input v-model="dataForm.countNumber" placeholder="个数"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="countPerLiter" label="个/L">
            <el-input v-model="dataForm.countPerLiter" placeholder="个/L"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="biomass" label="生物量">
            <el-input v-model="dataForm.biomass" placeholder="生物量"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  detectionOrder: "",
  stationName: "",
  concentratedVolume: "",
  phylum: "",
  className: "",
  orderName: "",
  familyName: "",
  genusName: "",
  species: "",
  latinName: "",
  wetWeight: "",
  countNumber: "",
  countPerLiter: "",
  biomass: ""
});

const rules = ref({
  detectionOrder: [
    { required: true, message: '检测次序不能为空', trigger: 'blur' }
  ],
  stationName: [
    { required: true, message: '测点名称不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/float-river-animal-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/float-river-animal-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
