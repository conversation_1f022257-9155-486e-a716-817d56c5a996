<template>
  <div class="mod-bga__phytodatanew">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">{{ $t("上传文件") }}</el-button>
      </el-form-item>
      <el-form-item>
        <img :src="previewState.imageUrl" class="preview-image" @load="previewState.isLoading = false" />
      </el-form-item>
      <el-upload action="http://*************.162:30838/api/v1/minio-upload" class="upload-demo" name="file" :limit="100" ref="upload" :auto-upload="false" :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-form-item>
        <input type="file" webkitdirectory @change="handleFolderSelect" ref="folderInput" accept="image/jpeg,image/jpg" />
        <el-button type="success" @click="handleReadFolderImageUpload">{{ $t("读取文件夹中的图片或视频数据并上传") }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <div style="display: flex; justify-content: left; align-items: start">
      <el-main>
        <div v-if="state.dataList && state.dataList.length > 0">
          <!-- 固定表头：动态计算高度以让表头固定 -->
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="region" label="区域" header-align="center" align="center"></el-table-column>
            <el-table-column prop="phylumName" label="门名称" header-align="center" align="center"></el-table-column>
            <el-table-column prop="genusName" label="属名称" header-align="center" align="center"></el-table-column>
            <el-table-column prop="speciesName" label="种名称" header-align="center" align="center"></el-table-column>
            <el-table-column prop="description" label="描述" header-align="center" align="center"></el-table-column>
            <el-table-column prop="dataSource" label="数据来源" header-align="center" align="center"></el-table-column>
            <el-table-column prop="imageAddress" label="原始图片地址minio" header-align="center" align="center"></el-table-column>
            <el-table-column prop="labelAddress" label="标注地址" header-align="center" align="center"></el-table-column>
            <el-table-column prop="time" label="日期" header-align="center" align="center"></el-table-column>

            <!-- 优化后的图片预览列 -->
            <el-table-column prop="previewAddress" label="预览/下载图片" header-align="center" align="center">
              <template v-slot="scope">
                <div class="image-preview-container">
                  <div v-if="scope.row.imageAddress" class="image-wrapper">
                    <el-image
                      :src="getImageUrl(scope.row.imageAddress)"
                      :preview-src-list="[getImageUrl(scope.row.imageAddress)]"
                      fit="cover"
                      class="table-image"
                      :loading="imageLoadingStates[scope.row.id] || 'loading'"
                      :hide-on-click-modal="true"
                      :preview-teleported="true"
                      @load="handleImageLoad(scope.row.id)"
                      @error="handleImageError(scope.row.id)"
                    >
                      <template #placeholder>
                        <div class="image-slot">
                          <i class="el-icon-picture"></i>
                          <span>加载中...</span>
                        </div>
                      </template>
                      <template #error>
                        <div class="image-slot">
                          <i class="el-icon-picture"></i>
                          <span>加载失败</span>
                        </div>
                      </template>
                    </el-image>
                    <div class="image-actions">

                      <el-button size="mini" @click="handleDownload(scope.row.imageAddress)">下载</el-button>
                    </div>
                  </div>
                  <div v-else class="no-image">
                    <i class="el-icon-picture"></i>
                    <span>无图片</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        </div>
        <el-container style="justify-content: center" v-if="state.dataList && state.dataList.length === 0">
          <el-empty :description="$t('noData')" :image-size="300" />
        </el-container>
      </el-main>
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>

    <!-- 图片预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      :title="previewTitle"
      width="70%"
      top="5vh"
      :close-on-click-modal="false"
      class="image-preview-dialog"
    >
      <div class="image-preview-content">
        <div v-if="previewLoading" class="preview-loading">
          <i class="el-icon-loading"></i>
          <span>图片加载中...</span>
        </div>
        <img
          v-else-if="previewImage"
          :src="previewImage"
          :alt="previewTitle"
          class="preview-image-large"
          @load="previewLoading = false"
          @error="handlePreviewError"
        />
        <div v-else class="preview-error">
          <el-empty description="图片加载失败"></el-empty>
        </div>
      </div>
      <template #footer>
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleDownload(currentPreviewImage)" :disabled="!currentPreviewImage">
          下载图片
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs, onMounted, onBeforeUnmount } from "vue";
  // 兼容部分组件没有 default export
  import AddOrUpdate from "./FileUpload-add-or-update.vue";
  import { ElLoading, ElMessage } from "element-plus";
  import { globalLanguage } from "@/utils/globalLang";
  import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  import baseService from "@/service/baseService";
  import { useRoute } from "vue-router";
  import axios from "axios";

  const { $t } = globalLanguage();
  const route = useRoute();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/file/fileupload/page",
    getDataListIsPage: true,
    exportURL: "/file/fileupload/export",
    deleteURL: "/file/fileupload",
    uploadURL: "/file/fileupload"
  });
  const state = reactive({ ...useView(view), ...toRefs(view) });

  // 图片加载状态管理
  const imageLoadingStates = ref<Record<string, boolean>>({});
  const imageErrorStates = ref<Record<string, boolean>>({});

  // 预览相关状态
  const previewVisible = ref(false);
  const previewImage = ref("");
  const previewLoading = ref(false);
  const previewTitle = ref("图片预览");
  const currentPreviewImage = ref("");

  const search = () => {
    state.dataForm!.year = "";
    state.dataForm!.month = "";
    state.getDataList();
  };

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };

  // 表格高度计算（用于固定表头）
  const tableHeight = ref<number>(400);
  const calculateTableHeight = () => {
    const offset = 260; // 根据页面顶部控件和分页预留高度，按需调整
    const h = window.innerHeight - offset;
    tableHeight.value = h > 200 ? h : 200;
  };

  onMounted(() => {
    calculateTableHeight();
    window.addEventListener("resize", calculateTableHeight);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", calculateTableHeight);
  });

  const state_excel = reactive({
    selectedFile: null as File | null,
    selectedFiles: [] as Array<{ raw: File; name: string; status: string; path: string }>,
    isUploading: false,
    folderName: "",
    classificationInfo: {
      phylumName: "",
      speciesName: ""
    }
  });

  // 获取图片完整URL
  const getImageUrl = (imageAddress: string) => {
    if (!imageAddress) return '';
    return `http://*************:9000/${imageAddress}`;
  };

  // 管理图片加载状态
  const getImageLoading = (id: string) => {
    return imageLoadingStates.value[id] !== false;
  };

  const handleImageLoad = (id: string) => {
    imageLoadingStates.value[id] = false;
    imageErrorStates.value[id] = false;
  };

  const handleImageError = (id: string) => {
    imageLoadingStates.value[id] = false;
    imageErrorStates.value[id] = true;
    console.error(`图片加载失败: ID ${id}`);
  };

  // 图片预览功能
  const handlePreviewImage = (imageAddress: string) => {
    if (!imageAddress) {
      ElMessage.warning("图片地址无效");
      return;
    }

    currentPreviewImage.value = imageAddress;
    previewImage.value = getImageUrl(imageAddress);
    previewLoading.value = true;
    previewVisible.value = true;

    // 从地址中提取文件名作为标题
    const fileName = imageAddress.split('/').pop() || '图片预览';
    previewTitle.value = fileName;
  };

  const handlePreviewError = () => {
    previewLoading.value = false;
    ElMessage.error("预览图片加载失败");
  };

  // 图片下载功能
  const handleDownload = async (imageAddress: string) => {
    if (!imageAddress) {
      ElMessage.warning("图片地址为空，无法下载");
      return;
    }

    try {
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '准备下载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const imageUrl = getImageUrl(imageAddress);
      const fileName = imageAddress.split('/').pop() || `download_${Date.now()}.jpg`;

      // 使用fetch获取图片blob
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        loadingInstance.close();
        ElMessage.success(`图片下载成功: ${fileName}`);
      }, 100);

    } catch (error) {
      console.error('下载失败:', error);
      ElMessage.error('图片下载失败，请检查网络连接或图片地址');

      // 备用方案：直接打开链接
      const fallbackUrl = getImageUrl(imageAddress);
      window.open(fallbackUrl, '_blank');
    }
  };

  // 初始化时重置所有图片加载状态
  onMounted(() => {
    if (state.dataList && state.dataList.length > 0) {
      state.dataList.forEach(item => {
        if (item.id) {
          imageLoadingStates.value[item.id] = true;
        }
      });
    }
  });

  // 原有函数保持不变
  function handleFileChange(file: any, fileList: any) {
    if (fileList.length > 0) {
      state_excel.selectedFile = fileList[0].raw;
    } else {
      state_excel.selectedFile = null;
    }
  }

  function handleFileUpload() {
    if (!state_excel.selectedFile) {
      ElMessage.error("请选择文件");
      return;
    }
    const formData = new FormData();
    formData.append("file", state_excel.selectedFile);
    baseService
      .post("files/upload", formData)
      .then((response) => {
        const FilePath = response.data.publicUrl;
        const url = new URL(FilePath);
        const objectName = url.pathname.substring(1);
        baseService.post("/file/fileupload/saveFileUploadPath", { filePath: objectName });
        ElMessage.success("上传成功");
        ElMessage.success("文件地址已存储");
      })
      .catch((error) => {
        console.error("错误:", error);
      });
  }

  const handleFolderSelect = async (e: Event) => {
    const input = e.target as HTMLInputElement;
    const files = Array.from(input.files || []);

    if (files.length > 0) {
      const firstFile = files[0];
      const fullPath = getFileRelativePath(firstFile);
      const folderName = fullPath?.split("/")[0] || "未命名文件夹";

      const classification = analyzeFolderStructure(files);

      state_excel.folderName = folderName;
      state_excel.classificationInfo = classification;
      state_excel.selectedFiles = files
        .filter((f) => f.name.match(/\.(jpe?g|png)$/i))
        .map((file) => ({
          raw: file,
          name: file.name,
          path: getFileRelativePath(file),
          status: "ready"
        }));
    }
  };

  function getFileRelativePath(file: File): string {
    return (file as any).webkitRelativePath ||
      (file as any).mozRelativePath ||
      (file as any).relativePath ||
      null;
  }

  function analyzeFolderStructure(files: File[]) {
    let phylumName = "";
    let speciesName = "";

    const pathSamples = files.slice(0, 3)
      .map(f => getFileRelativePath(f))
      .filter(Boolean) as string[];

    pathSamples.forEach(path => {
      const segments = path.split("/").slice(0, -1);

      for (const segment of segments) {
        if (!phylumName && segment.includes("门")) {
          phylumName = segment.trim();
          continue;
        }

        if (!speciesName && segment.includes("藻")) {
          speciesName = segment.trim();
        }
      }
    });

    return { phylumName, speciesName };
  }

  const handleReadFolderImageUpload = async () => {
    if (state_excel.selectedFiles.length === 0) {
      ElMessage.error("请选择包含图片的文件夹");
      return;
    }

    state_excel.isUploading = true;
    openLoading();

    try {
      const results = await Promise.all(
        state_excel.selectedFiles.map(async (file) => {
          try {
            file.status = "uploading";

            const filePath: string = file.path;
            if (!filePath) {
              ElMessage.warning(`文件 ${file.name} 路径信息缺失`);
              return false;
            }

            const segments = filePath.split("/").filter(Boolean);
            segments.pop();

            const dynamicClassification = {
              phylumName: [...segments].reverse().find(s => s.includes("门")) ||
                state_excel.classificationInfo.phylumName ||
                segments[0] || "未分类门",
              speciesName: [...segments].reverse().find(s => s.includes("藻")) ||
                state_excel.classificationInfo.speciesName ||
                ""
            };

            const formData = new FormData();
            formData.append("file", file.raw);

            const res = await axios.post(
              "http://*************:30838/api/v1/minio-upload",
              formData,
              {
                params: {
                  path: dynamicClassification.phylumName.replace(/\s+/g, '_')
                },
                headers: { "Content-Type": "multipart/form-data" }
              }
            );

            const ImagePath = res.data.data.filePath;

            await baseService.post("/file/fileupload/saveFileUploadPath", {
              filePath: ImagePath,
              phylumName: dynamicClassification.phylumName,
              speciesName: dynamicClassification.speciesName,
              originalPath: filePath
            });

            file.status = "success";
            return true;
          } catch (e) {
            file.status = "error";
            console.error(`文件 ${file.name} 上传失败:`, e);
            throw e;
          }
        })
      );

      ElMessage.success(`成功上传${results.length}个文件`);
    } catch (error) {
      console.error("批量上传失败:", error);
      ElMessage.error("上传过程中出现错误");
    } finally {
      closeLoading();
      state_excel.isUploading = false;
    }
  };

  const openLoading = () => {
    ElLoading.service({
      lock: true,
      text: "上传中",
      background: "rgba(0, 0, 0, 0.7)"
    });
  };

  const closeLoading = () => {
    ElLoading.service().close();
  };

  const downloadFile = () => {
    const downloadLink = document.createElement("a");
    downloadLink.href = "/PhytoExcelDataImport.xlsx";
    downloadLink.download = "浮游植物Excel数据表模板.xlsx";
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  };

  interface DownloadLinkRequest {
    bucketName?: string;
    objectName?: string;
    prefix?: string;
  }

  function handleFileDownLoad(params: DownloadLinkRequest) {
    params.bucketName = "data-bga";
    params.objectName = "550b4d836fDATA.xlsx";
    params.prefix = "data-bga-common/";
    axios
      .get("http://*************:30838/api/v1/minio-upload/download-link", { params: params })
      .then((response) => {
        const DownloadURL = response.data.data;
        const link = document.createElement("a");
        link.href = DownloadURL;
        link.download = params.objectName ?? "";
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        ElMessage.success("下载成功");
      })
      .catch((error) => {
        console.error("错误:", error);
      });
  }

  const previewState = reactive({
    imageUrl: "",
    isLoading: false,
    errorMsg: ""
  });

</script>

<style scoped>
  .mod-bga__phytodatanew {
    padding: 20px;
  }

  .image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80px;
  }

  .image-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .table-image {
    width: 100px;
    height: 60px;
    border-radius: 4px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
  }

  .table-image:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .image-actions {
    display: flex;
    gap: 5px;
  }

  .image-slot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 60px;
    background: #f5f7fa;
    color: #909399;
    border-radius: 4px;
  }

  .no-image {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 60px;
    background: #f5f7fa;
    color: #909399;
    border-radius: 4px;
  }

  .image-preview-dialog .el-dialog__body {
    padding: 0;
  }

  .image-preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    max-height: 70vh;
    overflow: hidden;
  }

  .preview-image-large {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  .preview-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #606266;
  }

  .preview-error {
    padding: 40px 0;
  }

  .el-radio-button {
    --el-radio-button-checked-bg-color: #edf9f8;
    --el-radio-button-checked-text-color: #17b3a3;
  }

  :deep(.month-card .el-radio-button__inner) {
    width: 300px;
    height: 60px;
    font-size: 18px;
    padding-top: 20px;
  }

  .preview-image {
    object-fit: contain;
    max-width: 20%;
    transition: all 0.3s;
  }
</style>
