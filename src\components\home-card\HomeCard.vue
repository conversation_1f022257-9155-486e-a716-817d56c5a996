<template>
  <div style="margin-bottom: 20px">
    <div class="grid" style="height: 90px">
      <template v-for="(item, index) in count" :key="item.value">
        <el-card
          :class="['card-panel', 'item' + index]"
          shadow="always"
          :body-style="{
            background: item.color,
            padding: '10px',
            height: '90px',
            boxSizing: 'border-box'
          }"
        >
          <div class="warp">
            <div class="text">{{ item.title }}</div>
            <div class="num">{{ item.value }}</div>
          </div>
          <div :class="['icon-people viteIcon', item.icon]"></div>
        </el-card>
      </template>
    </div>
    <div class="pie-chart-container">
      <el-card class="pie-chart-card">
        <pie-chart :chart-data="mainPieChartData" title="各类数据占比分布"></pie-chart>
      </el-card>
      <el-card class="pie-chart-card" v-if="otherPieChartData.length > 0">
        <pie-chart :chart-data="otherPieChartData" title="其他数据类型内部占比分布"></pie-chart>
      </el-card>
    </div>
    <!--    <el-card style="margin-top: 20px;">-->
    <!--      <pie-chart :chart-data="mainPieChartData" title="各类数据占比分布"></pie-chart>-->
    <!--    </el-card>-->
    <!--    <el-card style="margin-top: 20px;" v-if="otherPieChartData.length > 0">-->
    <!--      <pie-chart :chart-data="otherPieChartData" title="其他数据类型内部占比分布"></pie-chart>-->
    <!--    </el-card>-->
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import { useAppStore } from "@/store";
import baseService from "@/service/baseService";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const store = useAppStore();
import { formatDate } from "@/utils/format";
import PieChart from "@/components/charts/PieChart.vue";
const addressList = ref(store.state.address.list);
const todayPicture = ref([]);
const pieChartData = ref([]); // 饼图数据
const mainPieChartData = ref([]); // 第一个饼图数据
const otherPieChartData = ref([]); // 第二个饼图数据 (用于“其他”类别内部)
const count = reactive([
  {
    icon: "vitehome-zongshouyi",
    title: $t("bga.monitoringArea"),
    //value: addressList.value.length,
    value: 0,
    locale: "user",
    key: "user",
    color: "var(--el-color-primary)"
  },
  {
    icon: "vitehome-wengzhang",
    title: $t("bga.dataTotal"),
    value: 0,
    locale: "article",
    key: "shoppings",
    color: "#55bcff"
  },
  {
    icon: "vitehome-liulanliang",
    title: $t("bga.todayUpload"),
    value: "",
    locale: "page.view",
    key: "pageview",
    color: "#646cff "
  },
  {
    icon: "vitehome-done",
    title: $t("bga.todayTagged"),
    value: 0,
    key: "done",
    locale: "commit",
    color: "#F6A829 "
  }
]);
const dataForm1 = reactive({
  order: "desc",
  orderField: "create_date",
  page: 1,
  limit: 10,
  fileName: "",
  address: "",
  addressId: "",
  createDate: ""
});
const dataForm2 = reactive({
  order: "desc",
  orderField: "create_date",
  page: 1,
  limit: 10,
  fileName: "",
  address: "",
  addressId: "",
  createDate: ""
});
function getAllData() {
  let nowTime = formatDate();
  dataForm2.createDate = nowTime;
  Promise.all([
    baseService.get("/oss/files/page", dataForm1),
    baseService.get("/oss/files/page", dataForm2),
    baseService.get("/bga/interface_data_huti/page"), // 新增：获取湖体数据总数
    baseService.get("/task/interface_data_hedao_guokong/page"), // 新增：获取河道数据总数
    baseService.get("/task/interface_data_hedao_stbc/page"), // 新增：获取河道生态补偿数据总数
    baseService.get("/task/interface_data_szjhc/page"), // 新增：获取水质净化厂数据总数
    baseService.get("/bird/birdnumbercount/page"), // 新增：获取鸟类数据总数 bga/phytodatanew
    baseService.get("/benthos/zoobtdatanew/page"), //底栖动物数据总量
    baseService.get("/bga/phytodatanew/page"), //浮游植物数据总量
    baseService.get("/zooplankton/zptdatanew/page"), //浮游动物数据总量task
    baseService.get("/task/interface_data_dbk/page"), //大泊口气象站数据总量
    baseService.get("/bga/monitoringszjhcstation/page"), //监测站数据总量
    baseService.get("/file/fileupload/page"), //藻类图像数据总量
    baseService.get("/video/videoupload/page"), //藻类视频数据总量
    baseService.get("/task/interface_data_wq_dbk/page"), //大泊口水质数据总量
    baseService.get("/fish/fishnumbercount/page")//新增：获取鱼类数据总数 bga/phytodatanew
  ]).then(([total, todayData, hutiTotal, hedaoTotal, hedaoStbcTotal, szjhcTotal, birdTotal, zoobtTotal, phytoTotal, zptTotal, DbkQxTotal,
             monitoringszjhcstationTotal, algaeImageTotal, algaeVideoTotal, dbkWqTotal,fishTotal]) => {
    // 更新监测地区数量
    count[0].value = monitoringszjhcstationTotal.data.total;

    // 更新数据总数
    count[1].value = total.data.total + hutiTotal.data.total + hedaoTotal.data.total + hedaoStbcTotal.data.total + szjhcTotal.data.total + birdTotal.data.total
      + zoobtTotal.data.total + phytoTotal.data.total + zptTotal.data.total + DbkQxTotal.data.total + algaeImageTotal.data.total + algaeVideoTotal.data.total
      + dbkWqTotal.data.total+fishTotal.data.total;

    // 原始饼图数据（过滤掉值为0的项，避免它们影响计算和显示）
    const rawAllData = [
      { name: "湖体自动监测数据", value: hutiTotal.data.total, itemStyle: { color: "#409EFF" } },
      { name: "河道国控点自动监测数据", value: hedaoTotal.data.total, itemStyle: { color: "#646cff" } },
      { name: "河道生态补偿数据", value: hedaoStbcTotal.data.total, itemStyle: { color: "#F6A829" } },
      { name: "水质净化厂数据", value: szjhcTotal.data.total, itemStyle: { color: "#13ce66" } }, // 大头数据
      { name: "鸟类图像视频数据", value: birdTotal.data.total, itemStyle: { color: "#ff4949" } },
      { name: "底栖动物数据", value: zoobtTotal.data.total, itemStyle: { color: "#909399" } },
      { name: "浮游植物数据", value: phytoTotal.data.total, itemStyle: { color: "#B5D8F7" } },
      { name: "浮游动物数据", value: zptTotal.data.total, itemStyle: { color: "#F7B5B5" } },
      { name: "大泊口气象数据", value: DbkQxTotal.data.total, itemStyle: { color: "#55bcff" } },
      { name: "大泊口水质数据", value: dbkWqTotal.data.total, itemStyle: { color: "#9A60F3" } },
      { name: "藻类图像数据", value: algaeImageTotal.data.total, itemStyle: { color: "#F154DE" } },
      { name: "藻类视频数据", value: algaeVideoTotal.data.total, itemStyle: { color: "#FF7A45" } },
      { name: "鱼类数据", value: fishTotal.data.total, itemStyle: { color: "#FF7A45" } },
    ].filter((item) => item.value > 0); // 过滤掉数值为0的项

    let totalSum = rawAllData.reduce((sum, item) => sum + item.value, 0);

    const MIN_PERCENTAGE_THRESHOLD = 0.01; // 1%

    let tempMainPieData = [];
    let tempOtherPieData = []; // 用于收集小于阈值的数据
    let otherSum = 0; // 小于阈值的数据总和

    // 将原始数据按比例分类
    rawAllData.forEach((item) => {
      if (totalSum === 0 || item.value / totalSum < MIN_PERCENTAGE_THRESHOLD) {
        // 如果总和为0，或当前项占比小于阈值，则放入“其他”组
        tempOtherPieData.push(item);
        otherSum += item.value;
      } else {
        // 否则，直接添加到主饼图数据
        tempMainPieData.push(item);
      }
    });

    // 处理“其他”分类
    if (otherSum > 0) {
      tempMainPieData.push({
        name: `其他 (${tempOtherPieData.length}项)`, // 显示有多少项被合并
        value: otherSum,
        itemStyle: { color: '#cccccc' } // 给一个中性颜色
      });
    }

    mainPieChartData.value = tempMainPieData;
    otherPieChartData.value = tempOtherPieData; // 第二个饼图的数据就是这些被合并的项

    todayPicture.value = todayData.data.list;
    if (todayPicture.value.length) {
      const isLabel = todayPicture.value.filter((item) => item.area > 0);
      isLabel.length ? (count[3].value = isLabel.length) : (count[3].value = 0);
    }
  });
  const dailyStats = reactive({
    date: formatDate(),
    totals: {
      lake: 0, // 湖体数据
      river: 0, // 河道数据
      bird: 0, // 鸟类数据
      algaeImage: 0, // 藻类图像
      algaeVideo: 0 // 藻类视频
    },
    get total() {
      return Object.values(this.totals).reduce((sum, val) => sum + val, 0);
    }
  });
  const today = formatDate(new Date()).split(' ')[0];
  dailyStats.date = today;
  // 构建所有API请求
  const requests = [
    // 总量查询
    baseService.get("/bga/interface_data_huti/interface/getHutiAutoDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    }),
    baseService.get("/task/interface_data_hedao_guokong/interface/getHedaoAutoDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    }),
    baseService.get("/bird/birdnumbercount/interface/getBirdDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    }),
    baseService.get("/task/interface_data_dbk/interface/getDbkQxAutoDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    }),
    baseService.get("/task/interface_data_szjhc/interface/getSzjhcAutoDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    }),
    baseService.get("/task/interface_data_hedao_stbc/interface/getHedaoStbcDataApi", {
      startTime: today + " 00:00:00",
      endTime: today + " 23:59:59"
    })
  ];

  Promise.all(requests).then(([
        todayHuti,
        todayHedao,
        todayBird,
        todayAlgaeImage,
        todayAlgaeVideo
      ]) => {
      // 更新每日统计数据
      dailyStats.totals = {
        lake: todayHuti.data.total,
        river: todayHedao.data.total,
        bird: todayBird.data.total,
        algaeImage: todayAlgaeImage.data.total,
        algaeVideo: todayAlgaeVideo.data.total
      };
      // 更新到count[2]
      count[2].value = todayHuti.data.total + todayHedao.data.total + todayBird.data.total + todayAlgaeImage.data.total;
    })
    .catch((error) => {
      console.error("获取每日数据失败:", error);
      count[2].value = "数据加载失败";
    });
}
onMounted(() => {
  getAllData();
});
</script>

<style lang="scss" scoped>
.viteIcon {
  font-size: 60px;
  color: #fff;
}

.pie-chart-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;

  .pie-chart-card {
    flex: 1;
    min-width: 0; /* 防止flex item溢出 */

    /* 确保卡片内容高度一致 */
    :deep(.el-card__body) {
      height: 400px;
      display: flex;
      flex-direction: column;
    }
  }

  .empty-chart-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 50px;
      margin-bottom: 10px;
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-column-gap: 20px;

  .card-panel {
    font-size: 16px;
    height: 100%;
    // overflow: hidden;
    border-radius: 5px;
    position: relative;
    border: none;

    .icon-people {
      position: absolute;
      bottom: -8px;
      right: -8px;
      opacity: 0.3;
      transform: rotate(-25deg);
      transition: all 0.2s;
    }

    .warp {
      min-width: 100px;
      // text-align: center;
      color: #fff;

      .text {
        line-height: 18px;
        font-size: 16px;
        margin-bottom: 12px;
      }
      .num {
        font-size: 30px;
        width: 14px;
        margin: 20px 0;
      }
    }
  }

  .card-panel:hover .icon-people {
    bottom: 0;
    right: 0;
  }
}
</style>
