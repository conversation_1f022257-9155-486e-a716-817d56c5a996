<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item prop="year" label="年">
        <el-input v-model="dataForm.year" placeholder="年"></el-input>
      </el-form-item>
      <el-form-item prop="month" label="月">
        <el-input v-model="dataForm.month" placeholder="月"></el-input>
      </el-form-item>
      <el-form-item prop="day" label="日">
        <el-input v-model="dataForm.day" placeholder="日"></el-input>
      </el-form-item>
      <el-form-item prop="monitoringPoint" label="监测点位">
        <el-input v-model="dataForm.monitoringPoint" placeholder="监测点位"></el-input>
      </el-form-item>
      <el-form-item prop="transparency" label="透明度(cm)">
        <el-input v-model="dataForm.transparency" placeholder="透明度"></el-input>
      </el-form-item>
      <el-form-item prop="dissolvedOxygen" label="溶解氧(mg/L)">
        <el-input v-model="dataForm.dissolvedOxygen" placeholder="溶解氧"></el-input>
      </el-form-item>
      <el-form-item prop="waterTemperature" label="水温(℃)">
        <el-input v-model="dataForm.waterTemperature" placeholder="水温"></el-input>
      </el-form-item>
      <el-form-item prop="ph" label="pH值">
        <el-input v-model="dataForm.ph" placeholder="pH值"></el-input>
      </el-form-item>
      <el-form-item prop="totalNitrogen" label="总氮(mg/L)">
        <el-input v-model="dataForm.totalNitrogen" placeholder="总氮"></el-input>
      </el-form-item>
      <el-form-item prop="ammoniaNitrogen" label="氨氮(mg/L)">
        <el-input v-model="dataForm.ammoniaNitrogen" placeholder="氨氮"></el-input>
      </el-form-item>
      <el-form-item prop="totalPhosphorus" label="总磷(mg/L)">
        <el-input v-model="dataForm.totalPhosphorus" placeholder="总磷"></el-input>
      </el-form-item>
      <el-form-item prop="chlorophyllA" label="叶绿素a(μg/L)">
        <el-input v-model="dataForm.chlorophyllA" placeholder="叶绿素a"></el-input>
      </el-form-item>
      <el-form-item prop="chemicalOxygenDemand" label="化学需氧量(mg/L)">
        <el-input v-model="dataForm.chemicalOxygenDemand" placeholder="化学需氧量"></el-input>
      </el-form-item>
      <el-form-item prop="nitriteNitrogen" label="亚硝酸盐氮(mg/L)">
        <el-input v-model="dataForm.nitriteNitrogen" placeholder="亚硝酸盐氮"></el-input>
      </el-form-item>
      <el-form-item prop="nitrateNitrogen" label="硝酸盐氮(mg/L)">
        <el-input v-model="dataForm.nitrateNitrogen" placeholder="硝酸盐氮"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  year: "",
  month: "",
  day: "",
  monitoringPoint: "",
  transparency: "",
  dissolvedOxygen: "",
  waterTemperature: "",
  ph: "",
  totalNitrogen: "",
  ammoniaNitrogen: "",
  totalPhosphorus: "",
  chlorophyllA: "",
  chemicalOxygenDemand: "",
  nitriteNitrogen: "",
  nitrateNitrogen: ""
});

const rules = ref({
  year: [
    { required: true, message: '必填项不能为空', trigger: 'blur' }
  ],
  month: [
    { required: true, message: '必填项不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/task/interfaceDatashuiku/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/task/interfaceDatashuiku", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>