
## 1. 河流浮游植物数据 (FloatRiverPlantData)

### 基础路径: `/float-river-plant-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取河流浮游植物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增河流浮游植物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |



## 2. 水库浮游植物数据 (FloatShuikuPlantData)

### 基础路径: `/float-shuiku-plant-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取水库浮游植物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增水库浮游植物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 3. 河流浮游动物数据 (FloatRiverAnimalData)

### 基础路径: `/float-river-animal-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取河流浮游动物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增河流浮游动物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 4. 水库浮游动物数据 (FloatShuikuAnimalData)

### 基础路径: `/float-shuiku-animal-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取水库浮游动物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增水库浮游动物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 5. 滇池大型水生植物数据 (DianPoolLargePlantData)

### 基础路径: `/dian-pool-large-plant-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取滇池大型水生植物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增滇池大型水生植物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 6. 滇池鱼类群落调查数据 (DianPoolFishData)

### 基础路径: `/dian-pool-fish-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取滇池鱼类群落调查数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增滇池鱼类群落调查数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 7. 鸟类数据 (BirdData)

### 基础路径: `/bird-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取鸟类数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增鸟类数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 8. 草海水文数据 (HydrologyGrassSeaData)

### 基础路径: `/hydrology-grass-sea-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取草海水文数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增草海水文数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 9. 外海水文数据 (HydrologyOuterSeaData)

### 基础路径: `/hydrology-outer-sea-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取外海水文数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增外海水文数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 10. 入湖河流水文数据 (HydrologyInletRiverData)

### 基础路径: `/hydrology-inlet-river-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取入湖河流水文数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增入湖河流水文数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 11. 湖泊沉积物数据 (SedimentLakeData)

### 基础路径: `/sediment-lake-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取湖泊沉积物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增湖泊沉积物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 12. 河流沉积物数据 (SedimentRiverData)

### 基础路径: `/sediment-river-data`

| 接口名称 | HTTP方法 | 路径 | 描述 |
|---------|---------|------|------|
| 分页查询 | GET | `/page` | 分页获取河流沉积物数据列表 |
| 详情查询 | GET | `/{id}` | 根据ID获取单条数据详情 |
| 新增数据 | POST | `/` | 新增河流沉积物数据 |
| 更新数据 | PUT | `/{id}` | 更新指定ID的数据 |
| 删除数据 | DELETE | `/{id}` | 删除指定ID的数据 |
| 批量删除 | DELETE | `/batch` | 批量删除多条数据 |
| 导出Excel | GET | `/export` | 导出数据到Excel文件 |
| 导入Excel | POST | `/import` | 从Excel文件导入数据 |
| 时间范围查询 | GET | `/time-range` | 根据时间范围查询数据 |

## 通用请求参数说明

### 分页查询参数
| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|-------|------|------|------|-------|
| current | Integer | 否 | 当前页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| startTime | String | 否 | 开始时间 (yyyy-MM-dd) | - |
| endTime | String | 否 | 结束时间 (yyyy-MM-dd) | - |

### 时间范围查询参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| startTime | String | 是 | 开始时间 (yyyy-MM-dd) |
| endTime | String | 是 | 结束时间 (yyyy-MM-dd) |

### 批量删除请求体
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

