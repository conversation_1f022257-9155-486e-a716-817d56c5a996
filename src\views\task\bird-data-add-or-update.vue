<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="year" label="年">
            <el-input v-model="dataForm.year" placeholder="年"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="month" label="月">
            <el-input v-model="dataForm.month" placeholder="月"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="day" label="日">
            <el-input v-model="dataForm.day" placeholder="日"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="samplePlot" label="样地">
            <el-input v-model="dataForm.samplePlot" placeholder="样地"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="wetland" label="湿地">
            <el-input v-model="dataForm.wetland" placeholder="湿地"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="species" label="物种">
            <el-input v-model="dataForm.species" placeholder="物种"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="quantity" label="数量">
            <el-input v-model="dataForm.quantity" placeholder="数量"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="latinName" label="拉丁名">
            <el-input v-model="dataForm.latinName" placeholder="拉丁名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  year: "",
  month: "",
  day: "",
  samplePlot: "",
  wetland: "",
  species: "",
  latinName: "",
  quantity: ""
});

const rules = ref({
  year: [
    { required: true, message: '年份不能为空', trigger: 'blur' }
  ],
  month: [
    { required: true, message: '月份不能为空', trigger: 'blur' }
  ],
  day: [
    { required: true, message: '日期不能为空', trigger: 'blur' }
  ],
  species: [
    { required: true, message: '物种不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/bird-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/bird-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
