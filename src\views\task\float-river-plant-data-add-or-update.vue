<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="80%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="stationName" label="测点名称">
            <el-input v-model="dataForm.stationName" placeholder="测点名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="year" label="年">
            <el-input v-model="dataForm.year" placeholder="年"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="month" label="月">
            <el-input v-model="dataForm.month" placeholder="月"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="samplingVolume" label="采样体积">
            <el-input v-model="dataForm.samplingVolume" placeholder="采样体积"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="concentratedVolume" label="浓缩体积">
            <el-input v-model="dataForm.concentratedVolume" placeholder="浓缩体积"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="dilutionRatio" label="稀释倍数">
            <el-input v-model="dataForm.dilutionRatio" placeholder="稀释倍数"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="fieldArea" label="视野面积(cm²)">
            <el-input v-model="dataForm.fieldArea" placeholder="视野面积"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="phylum" label="门类">
            <el-input v-model="dataForm.phylum" placeholder="门类"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="className" label="纲">
            <el-input v-model="dataForm.className" placeholder="纲"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="orderName" label="目">
            <el-input v-model="dataForm.orderName" placeholder="目"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="familyName" label="科">
            <el-input v-model="dataForm.familyName" placeholder="科"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="genusName" label="属">
            <el-input v-model="dataForm.genusName" placeholder="属"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="species" label="种类">
            <el-input v-model="dataForm.species" placeholder="种类"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item prop="latinName" label="拉丁学名">
            <el-input v-model="dataForm.latinName" placeholder="拉丁学名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item prop="wetWeight" label="湿重">
            <el-input v-model="dataForm.wetWeight" placeholder="湿重"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="countNumber" label="个数">
            <el-input v-model="dataForm.countNumber" placeholder="个数"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="cellCount" label="细胞数">
            <el-input v-model="dataForm.cellCount" placeholder="细胞数"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="biomass" label="生物量">
            <el-input v-model="dataForm.biomass" placeholder="生物量"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  stationName: "",
  year: "",
  month: "",
  samplingVolume: "",
  concentratedVolume: "",
  dilutionRatio: "",
  fieldArea: "",
  phylum: "",
  className: "",
  orderName: "",
  familyName: "",
  genusName: "",
  species: "",
  latinName: "",
  wetWeight: "",
  countNumber: "",
  cellCount: "",
  biomass: ""
});

const rules = ref({
  stationName: [
    { required: true, message: '测点名称不能为空', trigger: 'blur' }
  ],
  year: [
    { required: true, message: '年份不能为空', trigger: 'blur' }
  ],
  month: [
    { required: true, message: '月份不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/float-river-plant-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/float-river-plant-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
