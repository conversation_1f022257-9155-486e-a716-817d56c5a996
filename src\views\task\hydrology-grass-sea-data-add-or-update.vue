<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="60%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="150px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="month" label="月份">
            <el-select v-model="dataForm.month" placeholder="请选择月份" style="width: 100%">
              <el-option label="1月" value="1"></el-option>
              <el-option label="2月" value="2"></el-option>
              <el-option label="3月" value="3"></el-option>
              <el-option label="4月" value="4"></el-option>
              <el-option label="5月" value="5"></el-option>
              <el-option label="6月" value="6"></el-option>
              <el-option label="7月" value="7"></el-option>
              <el-option label="8月" value="8"></el-option>
              <el-option label="9月" value="9"></el-option>
              <el-option label="10月" value="10"></el-option>
              <el-option label="11月" value="11"></el-option>
              <el-option label="12月" value="12"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="grassSeaWaterLevel" label="草海水位(m)">
            <el-input v-model="dataForm.grassSeaWaterLevel" placeholder="草海水位"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="grassSeaStorageCapacity" label="草海库容(亿方)">
            <el-input v-model="dataForm.grassSeaStorageCapacity" placeholder="草海库容"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  month: "",
  grassSeaWaterLevel: "",
  grassSeaStorageCapacity: ""
});

const rules = ref({
  month: [
    { required: true, message: '月份不能为空', trigger: 'change' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/hydrology-grass-sea-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/hydrology-grass-sea-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
