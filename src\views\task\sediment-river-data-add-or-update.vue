<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="90%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item prop="year" label="年">
            <el-input v-model="dataForm.year" placeholder="年"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="month" label="月">
            <el-input v-model="dataForm.month" placeholder="月"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="day" label="日">
            <el-input v-model="dataForm.day" placeholder="日"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="monitoringPoint" label="监测点位">
            <el-input v-model="dataForm.monitoringPoint" placeholder="监测点位"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="sampleState" label="样品状态">
            <el-input v-model="dataForm.sampleState" placeholder="样品状态"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="solubleTotalPhosphorus" label="可溶性总磷">
            <el-input v-model="dataForm.solubleTotalPhosphorus" placeholder="可溶性总磷"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="waterContent" label="含水率">
            <el-input v-model="dataForm.waterContent" placeholder="含水率"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="organicMatter" label="有机质">
            <el-input v-model="dataForm.organicMatter" placeholder="有机质"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="phValue" label="pH值">
            <el-input v-model="dataForm.phValue" placeholder="pH值"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="totalNitrogen" label="全氮">
            <el-input v-model="dataForm.totalNitrogen" placeholder="全氮"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="nitriteNitrogen" label="亚硝酸盐氮">
            <el-input v-model="dataForm.nitriteNitrogen" placeholder="亚硝酸盐氮"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="nitrateNitrogen" label="硝酸盐氮">
            <el-input v-model="dataForm.nitrateNitrogen" placeholder="硝酸盐氮"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="chromium" label="铬">
            <el-input v-model="dataForm.chromium" placeholder="铬"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="copper" label="铜">
            <el-input v-model="dataForm.copper" placeholder="铜"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="zinc" label="锌">
            <el-input v-model="dataForm.zinc" placeholder="锌"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="totalPhosphorus" label="全磷">
            <el-input v-model="dataForm.totalPhosphorus" placeholder="全磷"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="ferricOxide" label="三氧化二铁">
            <el-input v-model="dataForm.ferricOxide" placeholder="三氧化二铁"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="cadmium" label="镉">
            <el-input v-model="dataForm.cadmium" placeholder="镉"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="lead" label="铅">
            <el-input v-model="dataForm.lead" placeholder="铅"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="arsenic" label="砷">
            <el-input v-model="dataForm.arsenic" placeholder="砷"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="mercury" label="汞">
            <el-input v-model="dataForm.mercury" placeholder="汞"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  year: "",
  month: "",
  day: "",
  monitoringPoint: "",
  sampleState: "",
  solubleTotalPhosphorus: "",
  waterContent: "",
  organicMatter: "",
  phValue: "",
  totalNitrogen: "",
  nitriteNitrogen: "",
  nitrateNitrogen: "",
  chromium: "",
  copper: "",
  zinc: "",
  totalPhosphorus: "",
  ferricOxide: "",
  cadmium: "",
  lead: "",
  arsenic: "",
  mercury: ""
});

const rules = ref({
  year: [
    { required: true, message: '年份不能为空', trigger: 'blur' }
  ],
  month: [
    { required: true, message: '月份不能为空', trigger: 'blur' }
  ],
  day: [
    { required: true, message: '日期不能为空', trigger: 'blur' }
  ],
  monitoringPoint: [
    { required: true, message: '监测点位不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sediment-river-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/sediment-river-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
