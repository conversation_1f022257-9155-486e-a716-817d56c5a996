-- 创建12个数据表的SQL脚本
-- 根据任务.md中的字段要求创建

-- 1. 河道浮游植物数据表
CREATE TABLE `float_river_plant_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_name` varchar(100) DEFAULT NULL COMMENT '测点名称',
  `year` varchar(10) DEFAULT NULL COMMENT '年',
  `month` varchar(10) DEFAULT NULL COMMENT '月',
  `sampling_volume` varchar(50) DEFAULT NULL COMMENT '采样体积',
  `concentrated_volume` varchar(50) DEFAULT NULL COMMENT '浓缩体积',
  `dilution_ratio` varchar(50) DEFAULT NULL COMMENT '稀释倍数',
  `field_area` varchar(50) DEFAULT NULL COMMENT '视野面积（cm2)',
  `phylum` varchar(100) DEFAULT NULL COMMENT '门类',
  `class_name` varchar(100) DEFAULT NULL COMMENT '纲',
  `order_name` varchar(100) DEFAULT NULL COMMENT '目',
  `family_name` varchar(100) DEFAULT NULL COMMENT '科',
  `genus_name` varchar(100) DEFAULT NULL COMMENT '属',
  `species` varchar(100) DEFAULT NULL COMMENT '种类',
  `latin_name` varchar(200) DEFAULT NULL COMMENT '拉丁学名',
  `wet_weight` varchar(50) DEFAULT NULL COMMENT '湿重',
  `count_number` varchar(50) DEFAULT NULL COMMENT '个数',
  `cell_count` varchar(50) DEFAULT NULL COMMENT '细胞数',
  `biomass` varchar(50) DEFAULT NULL COMMENT '生物量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='河道浮游植物数据';

-- 2. 水库浮游植物数据表
CREATE TABLE `float_shuiku_plant_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_name` varchar(100) DEFAULT NULL COMMENT '测点名称',
  `year` varchar(10) DEFAULT NULL COMMENT '年',
  `month` varchar(10) DEFAULT NULL COMMENT '月',
  `sampling_volume` varchar(50) DEFAULT NULL COMMENT '采样体积',
  `concentrated_volume` varchar(50) DEFAULT NULL COMMENT '浓缩体积',
  `dilution_ratio` varchar(50) DEFAULT NULL COMMENT '稀释倍数',
  `field_area` varchar(50) DEFAULT NULL COMMENT '视野面积（cm2)',
  `phylum` varchar(100) DEFAULT NULL COMMENT '门类',
  `class_name` varchar(100) DEFAULT NULL COMMENT '纲',
  `order_name` varchar(100) DEFAULT NULL COMMENT '目',
  `family_name` varchar(100) DEFAULT NULL COMMENT '科',
  `genus_name` varchar(100) DEFAULT NULL COMMENT '属',
  `species` varchar(100) DEFAULT NULL COMMENT '种类',
  `latin_name` varchar(200) DEFAULT NULL COMMENT '拉丁学名',
  `wet_weight` varchar(50) DEFAULT NULL COMMENT '湿重',
  `count_number` varchar(50) DEFAULT NULL COMMENT '个数',
  `cell_count` varchar(50) DEFAULT NULL COMMENT '细胞数',
  `biomass` varchar(50) DEFAULT NULL COMMENT '生物量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水库浮游植物数据';

-- 3. 河道浮游动物数据表
CREATE TABLE `float_river_animal_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `detection_order` varchar(50) DEFAULT NULL COMMENT '检测次序',
  `station_name` varchar(100) DEFAULT NULL COMMENT '测点名称',
  `concentrated_volume` varchar(50) DEFAULT NULL COMMENT '浓缩体积',
  `phylum` varchar(100) DEFAULT NULL COMMENT '门',
  `class_name` varchar(100) DEFAULT NULL COMMENT '纲',
  `order_name` varchar(100) DEFAULT NULL COMMENT '目',
  `family_name` varchar(100) DEFAULT NULL COMMENT '科',
  `genus_name` varchar(100) DEFAULT NULL COMMENT '属',
  `species` varchar(100) DEFAULT NULL COMMENT '种类',
  `latin_name` varchar(200) DEFAULT NULL COMMENT '拉丁文',
  `wet_weight` varchar(50) DEFAULT NULL COMMENT '湿重',
  `count_number` varchar(50) DEFAULT NULL COMMENT '个数',
  `count_per_liter` varchar(50) DEFAULT NULL COMMENT '个/L',
  `biomass` varchar(50) DEFAULT NULL COMMENT '生物量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='河道浮游动物数据';

-- 4. 水库浮游动物数据表
CREATE TABLE `float_shuiku_animal_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `detection_order` varchar(50) DEFAULT NULL COMMENT '检测次序',
  `station_name` varchar(100) DEFAULT NULL COMMENT '测点名称',
  `concentrated_volume` varchar(50) DEFAULT NULL COMMENT '浓缩体积',
  `phylum` varchar(100) DEFAULT NULL COMMENT '门',
  `class_name` varchar(100) DEFAULT NULL COMMENT '纲',
  `order_name` varchar(100) DEFAULT NULL COMMENT '目',
  `family_name` varchar(100) DEFAULT NULL COMMENT '科',
  `genus_name` varchar(100) DEFAULT NULL COMMENT '属',
  `species` varchar(100) DEFAULT NULL COMMENT '种类',
  `latin_name` varchar(200) DEFAULT NULL COMMENT '拉丁文',
  `wet_weight` varchar(50) DEFAULT NULL COMMENT '湿重',
  `count_number` varchar(50) DEFAULT NULL COMMENT '个数',
  `count_per_liter` varchar(50) DEFAULT NULL COMMENT '个/L',
  `biomass` varchar(50) DEFAULT NULL COMMENT '生物量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水库浮游动物数据';

-- 5. 滇池大型水生植物分布面积、生物量数据表
CREATE TABLE `dian_pool_large_plant_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `time_period` varchar(50) DEFAULT NULL COMMENT '时间',
  `biomass_fresh_weight` varchar(50) DEFAULT NULL COMMENT '生物量鲜重（104t）',
  `submerged_plant_area_ratio` varchar(50) DEFAULT NULL COMMENT '沉水植物面积占比/(%)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='滇池大型水生植物分布面积、生物量';

-- 6. 滇池鱼类群落调查数据表
CREATE TABLE `dian_pool_fish_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `time_period` varchar(50) DEFAULT NULL COMMENT '时间',
  `sampling_point` varchar(100) DEFAULT NULL COMMENT '采样点',
  `net_type` varchar(100) DEFAULT NULL COMMENT '网具类型',
  `net_quantity` varchar(50) DEFAULT NULL COMMENT '网具数量',
  `duration` varchar(50) DEFAULT NULL COMMENT '时长',
  `fish_species` varchar(100) DEFAULT NULL COMMENT '鱼类种类',
  `serial_number` varchar(50) DEFAULT NULL COMMENT '编号',
  `total_length` varchar(50) DEFAULT NULL COMMENT '全长 (mm)',
  `body_length` varchar(50) DEFAULT NULL COMMENT '体长 (mm)',
  `body_weight` varchar(50) DEFAULT NULL COMMENT '体重 (g)',
  `quantity` varchar(50) DEFAULT NULL COMMENT '数量',
  `weight` varchar(50) DEFAULT NULL COMMENT '重量（g）',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='滇池鱼类群落调查数据';

-- 7. 鸟类数据表
CREATE TABLE `bird_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `year` varchar(10) DEFAULT NULL COMMENT '年',
  `month` varchar(10) DEFAULT NULL COMMENT '月',
  `day` varchar(10) DEFAULT NULL COMMENT '日',
  `sample_plot` varchar(100) DEFAULT NULL COMMENT '样地',
  `wetland` varchar(100) DEFAULT NULL COMMENT '湿地',
  `species` varchar(100) DEFAULT NULL COMMENT '物种',
  `latin_name` varchar(200) DEFAULT NULL COMMENT '拉丁名',
  `quantity` varchar(50) DEFAULT NULL COMMENT '数量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鸟类数据';

-- 8. 草海水文数据表
CREATE TABLE `hydrology_grassSea_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `month` varchar(10) DEFAULT NULL COMMENT '月份',
  `grass_sea_water_level` varchar(50) DEFAULT NULL COMMENT '草海水位m',
  `grass_sea_storage_capacity` varchar(50) DEFAULT NULL COMMENT '草海库容亿方',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='草海水文数据';

-- 9. 外海水文数据表
CREATE TABLE `hydrology_outerSea_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `month` varchar(10) DEFAULT NULL COMMENT '月份',
  `outer_sea_water_level` varchar(50) DEFAULT NULL COMMENT '外海水位m',
  `outer_sea_storage_capacity` varchar(50) DEFAULT NULL COMMENT '外海库容亿方',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外海水文数据';

-- 10. 入湖河道水文数据表
CREATE TABLE `hydrology_inletRiver_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `month` varchar(10) DEFAULT NULL COMMENT '月份',
  `niulan_river_to_grass_sea` varchar(50) DEFAULT NULL COMMENT '牛栏江入草海',
  `niulan_river_to_outer_sea` varchar(50) DEFAULT NULL COMMENT '牛栏江入外海',
  `niulan_river_water_supply` varchar(50) DEFAULT NULL COMMENT '牛栏江补水万方',
  `haikou_gate_discharge` varchar(50) DEFAULT NULL COMMENT '海口闸下泄量万方',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入湖河道水文数据';

-- 11. 湖体沉积物数据表
CREATE TABLE `sediment_lake_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `year` varchar(10) DEFAULT NULL COMMENT '年',
  `month` varchar(10) DEFAULT NULL COMMENT '月',
  `day` varchar(10) DEFAULT NULL COMMENT '日',
  `sample_point_name` varchar(100) DEFAULT NULL COMMENT '样点名称',
  `sample_state` varchar(50) DEFAULT NULL COMMENT '样品状态',
  `soluble_total_phosphorus` varchar(50) DEFAULT NULL COMMENT '可溶性总磷mg/kg',
  `water_content` varchar(50) DEFAULT NULL COMMENT '含水率（%）',
  `organic_matter` varchar(50) DEFAULT NULL COMMENT '有机质（%）',
  `ph_value` varchar(50) DEFAULT NULL COMMENT 'pH值（无量纲）',
  `total_nitrogen` varchar(50) DEFAULT NULL COMMENT '全氮',
  `nitrite_nitrogen` varchar(50) DEFAULT NULL COMMENT '亚硝酸盐氮',
  `nitrate_nitrogen` varchar(50) DEFAULT NULL COMMENT '硝酸盐氮',
  `chromium` varchar(50) DEFAULT NULL COMMENT '铬',
  `copper` varchar(50) DEFAULT NULL COMMENT '铜',
  `zinc` varchar(50) DEFAULT NULL COMMENT '锌',
  `total_phosphorus` varchar(50) DEFAULT NULL COMMENT '全磷',
  `ferric_oxide` varchar(50) DEFAULT NULL COMMENT '三氧化二铁（以Fe计）',
  `cadmium` varchar(50) DEFAULT NULL COMMENT '镉',
  `lead` varchar(50) DEFAULT NULL COMMENT '铅',
  `arsenic` varchar(50) DEFAULT NULL COMMENT '砷',
  `mercury` varchar(50) DEFAULT NULL COMMENT '汞',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='湖体沉积物数据';

-- 12. 河道沉积物数据表
CREATE TABLE `sediment_river_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `year` varchar(10) DEFAULT NULL COMMENT '年',
  `month` varchar(10) DEFAULT NULL COMMENT '月',
  `day` varchar(10) DEFAULT NULL COMMENT '日',
  `monitoring_point` varchar(100) DEFAULT NULL COMMENT '监测点位',
  `sample_state` varchar(50) DEFAULT NULL COMMENT '样品状态',
  `soluble_total_phosphorus` varchar(50) DEFAULT NULL COMMENT '可溶性总磷mg/kg',
  `water_content` varchar(50) DEFAULT NULL COMMENT '含水率（%）',
  `organic_matter` varchar(50) DEFAULT NULL COMMENT '有机质（%）',
  `ph_value` varchar(50) DEFAULT NULL COMMENT 'pH值（无量纲）',
  `total_nitrogen` varchar(50) DEFAULT NULL COMMENT '全氮',
  `nitrite_nitrogen` varchar(50) DEFAULT NULL COMMENT '亚硝酸盐氮',
  `nitrate_nitrogen` varchar(50) DEFAULT NULL COMMENT '硝酸盐氮',
  `chromium` varchar(50) DEFAULT NULL COMMENT '铬',
  `copper` varchar(50) DEFAULT NULL COMMENT '铜',
  `zinc` varchar(50) DEFAULT NULL COMMENT '锌',
  `total_phosphorus` varchar(50) DEFAULT NULL COMMENT '全磷',
  `ferric_oxide` varchar(50) DEFAULT NULL COMMENT '三氧化二铁（以Fe计）',
  `cadmium` varchar(50) DEFAULT NULL COMMENT '镉',
  `lead` varchar(50) DEFAULT NULL COMMENT '铅',
  `arsenic` varchar(50) DEFAULT NULL COMMENT '砷',
  `mercury` varchar(50) DEFAULT NULL COMMENT '汞',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='河道沉积物数据';