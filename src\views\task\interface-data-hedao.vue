<template>
  <div class="mod-bga__wqyzw">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button  type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button  type="danger" @click="state.deleteHandle()">删除</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="downloadFile">下载模板</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">{{ $t("导入Excel数据至数据库") }}</el-button>
      </el-form-item>
      <el-upload action="task/interfaceDatahedao/excel-input" class="upload-demo" name="file" :limit="1" ref="upload" :auto-upload="false" accept=".xlsx,.xls,.xlsm" :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
    </el-form>
    <!-- 弹窗, 新增 / 修改 -->
    <div style="display: flex; justify-content: left; align-items: start">
      <el-tree style="max-width: 300px; min-width: 300px" :data="treeData" :props="defaultProps" accordion @node-click="handleNodeClick" />
      <el-main>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" style="margin-top: 20px">
          <el-form-item>
            <el-form-item>
              <el-input style="width: 200px" v-model="state.dataForm.station" placeholder="测站名称" clearable></el-input>
            </el-form-item>
            <!--时间查询-->
            <el-form-item label="开始时间">
              <el-date-picker v-model="state.dataForm.startTime" type="month" placeholder="选择开始年月" format="YYYY-MM" value-format="YYYY-MM"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker v-model="state.dataForm.endTime" type="month" placeholder="选择结束年月" format="YYYY-MM" value-format="YYYY-MM"></el-date-picker>
            </el-form-item>
            <!--时间查询-->
            <el-form-item>
              <el-button @click="search">{{ $t("query") }}</el-button>
            </el-form-item>
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
          </el-form-item>
          
        </el-form>
        <div v-if="state.dataList && state.dataList.length > 0">
          <!-- 固定表头并固定监测站点名称列 -->
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" :height="tableHeight">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column width="220" prop="station" label="监测站点名称" header-align="center" align="center" fixed="left"></el-table-column>
            <el-table-column prop="year" label="年" header-align="center" align="center"></el-table-column>
            <el-table-column prop="month" label="月" header-align="center" align="center"></el-table-column>
            <el-table-column prop="day" label="日" header-align="center" align="center"></el-table-column>
            <el-table-column prop="transparency" label="透明度" header-align="center" align="center"></el-table-column>
            <el-table-column prop="dissolvedOxygen" label="溶解氧" header-align="center" align="center"></el-table-column>
            <el-table-column prop="waterTemperature" label="水温" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ph" label="PH" header-align="center" align="center"></el-table-column>
            <el-table-column prop="totalNitrogen" label="总氮" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ammoniaNitrogen" label="氨氮" header-align="center" align="center"></el-table-column>
            <el-table-column prop="totalPhosphorus" label="总磷" header-align="center" align="center"></el-table-column>
            <el-table-column prop="chlorophyllA" label="叶绿素a" header-align="center" align="center"></el-table-column>
            <el-table-column prop="chemicalOxygenDemand" label="化学需氧量" header-align="center" align="center"></el-table-column>
            <el-table-column prop="nitriteNitrogen" label="亚硝酸盐氮" header-align="center" align="center"></el-table-column>
            <el-table-column prop="orthophosphate" label="硝酸盐氮" header-align="center" align="center"></el-table-column>
            <el-table-column prop="high" label="水深" header-align="center" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        </div>
        <el-container style="justify-content: center" v-if="state.dataList && state.dataList.length === 0"> <el-empty :description="$t('noData')" :image-size="300" /> </el-container>
      </el-main>
    </div>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs, onMounted, nextTick, onBeforeUnmount } from "vue";
  // 兼容没有 default export 的组件
  import AddOrUpdate from "./interface-data-hedao-add-or-update.vue";
  import { ElLoading } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router"; // 导入 useRoute
const { $t } = globalLanguage();
const route = useRoute();
const router = useRouter();
const condition = ref<any>(null);
const year_now = ref<any>(new Date().getFullYear());
const years = reactive<any>([]);
interface Tree {
  label: string;
  children?: Tree[];
}
const handleNodeClick = (data: { label: string }, getNode: { parent: { label: string, parent?: { label: string } }, data: { children: any[] } }) => {
  console.log("接收到的 station:", data.label); // 检查数据是否完整
  const month = getNode.parent.label;
  const year = month ? getNode.parent.parent?.label : undefined;

  if (getNode.data.children.length === 0) {
    console.log("查询", year, month, data.label);
    if (year === undefined && month === undefined) {
      state.dataForm.year = data.label;
      state.getDataList();
      return;
    }
    if (year === undefined) {
      state.dataForm.year = month;
      state.dataForm.month = data.label;
      state.getDataList();
      return;
    }
    state.dataForm.year = year;
    state.dataForm.month = month;
    state.dataForm.station = data.label;
    state.getDataList();
  }
};
const treeData = reactive<Tree[]>([]); // null>([]); // 定义一个响应式变量用于存储树形数据

// 获取树形数据
const getTreeData = () => {
  baseService.get("/task/conditionhedao/list", { limit: 100000 }).then((res) => {
    // 假设 res.data 是服务器返回的数据
    const data = res.data.map((item: any) => ({
      label: item.label,
      children: item.children || [] // 确保 children 是一个数组
    }));
    console.log("data:", data);
    //先清空treeData数组
    treeData.splice(0, treeData.length);
    // 将获取到的数据赋值给 treeData
    treeData.push(...data); // 使用 push 将元素添加到响应式数组中
  });
};
// 获取月份数据
getTreeData();
const defaultProps = {
  children: "children",
  label: "label"
};

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/interfaceDatahedao/page",
  getDataListIsPage: true,
  exportURL: "/task/interfaceDatahedao/export",
  deleteURL: "/task/interfaceDatahedao",
  uploadURL: "/task/interfaceDatahedao/upload",
  dataForm: {
    year: "",
    month: "",
    station: "",
    startTime: "", // 开始时间
    endTime: "" // 结束时间
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

// 计算表格高度，留出顶部空间
const tableHeight = ref<number>(400);
const calculateTableHeight = () => {
  const offset = 260; // 根据顶部表单与分页高度调整
  const h = window.innerHeight - offset;
  tableHeight.value = h > 200 ? h : 200;
};

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

  // 页面加载时检查路由参数并自动查询
  onMounted(() => {
    if (route.query.station) {
      state.dataForm.station = route.query.station as string;
      // 如果是从monitoringszjhcstation页面跳转过来的，自动执行查询
      if (route.query.from === 'monitoring') {
        // 使用nextTick确保DOM更新后再执行查询
        nextTick(() => {
          state.getDataList();
        });
      }
    }
  });

const search = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

const state_excel = reactive({
  selectedFile: null
});

function handleFileChange(file: any, fileList: any) {
  // 当用户选择文件时，更新selectedFile
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw; // 使用 fileList[0].raw 获取原始 File 对象
  } else {
    state_excel.selectedFile = null; // 如果没有文件，则设置为 null
  }
}

function handleFileUpload() {
  //判断state_excel.selectedFile是否有值
  if (!state_excel.selectedFile) {
    ElMessage.error("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  openLoading();
  baseService
    .post("/task/interfaceDatahedao/excel-input", formData)
    .then((response) => {
      ElMessage.success("上传成功");
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    })
    .catch((error) => {
      console.error("错误:", error);
      ElMessage.success("上传成功");
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    });
}
const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};
const closeLoading = () => {
  ElLoading.service().close();
};
const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/InterfacedatahedaoExcelDataImport.xlsx"; // 文件相对于 public 文件夹的路径
  downloadLink.download = "水质河道Excel数据表模板.xlsx"; // 下载时的文件名

  // 模拟点击下载链接
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};
</script>

<style scoped>
  .el-radio-button {
    --el-radio-button-checked-bg-color: #edf9f8;
    --el-radio-button-checked-text-color: #17b3a3;
  }
  :deep(.month-card .el-radio-button__inner) {
    width: 300px;
    height: 60px;
    font-size: 18px;
    padding-top: 20px;
  }
</style>

