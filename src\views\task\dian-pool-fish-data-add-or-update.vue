<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="80%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="timePeriod" label="时间">
            <el-input v-model="dataForm.timePeriod" placeholder="时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="samplingPoint" label="采样点">
            <el-input v-model="dataForm.samplingPoint" placeholder="采样点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="netType" label="网具类型">
            <el-input v-model="dataForm.netType" placeholder="网具类型"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="netQuantity" label="网具数量">
            <el-input v-model="dataForm.netQuantity" placeholder="网具数量"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="duration" label="时长">
            <el-input v-model="dataForm.duration" placeholder="时长"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="fishSpecies" label="鱼类种类">
            <el-input v-model="dataForm.fishSpecies" placeholder="鱼类种类"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="serialNumber" label="编号">
            <el-input v-model="dataForm.serialNumber" placeholder="编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="totalLength" label="全长(mm)">
            <el-input v-model="dataForm.totalLength" placeholder="全长"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="bodyLength" label="体长(mm)">
            <el-input v-model="dataForm.bodyLength" placeholder="体长"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="bodyWeight" label="体重(g)">
            <el-input v-model="dataForm.bodyWeight" placeholder="体重"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="quantity" label="数量">
            <el-input v-model="dataForm.quantity" placeholder="数量"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="weight" label="重量(g)">
            <el-input v-model="dataForm.weight" placeholder="重量"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  timePeriod: "",
  samplingPoint: "",
  netType: "",
  netQuantity: "",
  duration: "",
  fishSpecies: "",
  serialNumber: "",
  totalLength: "",
  bodyLength: "",
  bodyWeight: "",
  quantity: "",
  weight: ""
});

const rules = ref({
  timePeriod: [
    { required: true, message: '时间不能为空', trigger: 'blur' }
  ],
  samplingPoint: [
    { required: true, message: '采样点不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/dian-pool-fish-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dian-pool-fish-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
