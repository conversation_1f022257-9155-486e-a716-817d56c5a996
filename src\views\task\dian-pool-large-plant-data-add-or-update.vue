<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="60%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="150px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="timePeriod" label="时间">
            <el-input v-model="dataForm.timePeriod" placeholder="时间"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="biomassFreshWeight" label="生物量鲜重(10⁴t)">
            <el-input v-model="dataForm.biomassFreshWeight" placeholder="生物量鲜重"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="submergedPlantAreaRatio" label="沉水植物面积占比(%)">
            <el-input v-model="dataForm.submergedPlantAreaRatio" placeholder="沉水植物面积占比"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  timePeriod: "",
  biomassFreshWeight: "",
  submergedPlantAreaRatio: ""
});

const rules = ref({
  timePeriod: [
    { required: true, message: '时间不能为空', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/dian-pool-large-plant-data/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dian-pool-large-plant-data", dataForm).then((res) => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
